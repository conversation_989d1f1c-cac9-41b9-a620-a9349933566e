# LaTeX 数学公式渲染支持

## 概述

ChatSQL 项目现已支持在 Markdown 内容中渲染 LaTeX 数学公式。该功能基于 KaTeX 库实现，提供快速、高质量的数学公式渲染。

## 功能特性

- ✅ 支持行内数学公式 (`$...$`)
- ✅ 支持块级数学公式 (`$$...$$`)
- ✅ 自动错误处理和降级显示
- ✅ 与现有主题系统完全兼容
- ✅ TypeScript 类型安全
- ✅ 性能优化的渲染

## 使用方法

### 行内公式

在文本中使用单个美元符号包围数学公式：

```markdown
这是一个行内公式：$E = mc^2$，爱因斯坦的质能方程。
```

### 块级公式

使用双美元符号创建居中的块级公式：

```markdown
$$\int_{-\infty}^{\infty} e^{-x^2} dx = \sqrt{\pi}$$
```

### 复杂公式示例

#### 矩阵
```latex
$$\begin{pmatrix}
a & b \\
c & d
\end{pmatrix}$$
```

#### 分数和根式
```latex
$$x = \frac{-b \pm \sqrt{b^2 - 4ac}}{2a}$$
```

#### 求和和积分
```latex
$$\sum_{i=1}^{n} x_i = \int_0^1 f(x) dx$$
```

## 技术实现

### 依赖库
- `katex`: 核心 LaTeX 渲染引擎
- `react-katex`: React 集成组件
- `remark-math`: Markdown 数学公式解析
- `rehype-katex`: HTML 转换插件

### 核心组件
- `MarkdownRenderer.tsx`: 主要的 Markdown 渲染组件，已集成 LaTeX 支持
- `latexRenderer.ts`: LaTeX 解析和渲染工具函数
- `LatexRenderer.tsx`: 独立的 LaTeX 渲染 React 组件

### 样式集成
LaTeX 公式样式已与项目的 CSS 变量系统集成：
- `--primary-text`: 主要文本颜色
- `--secondary-text`: 次要文本颜色
- `--accent-color`: 强调色（用于运算符）
- `--error-color`: 错误提示颜色

## 错误处理

当 LaTeX 公式语法错误时，系统会：
1. 在控制台输出警告信息
2. 显示带有错误提示的占位符
3. 不影响页面其他内容的正常显示

## 测试页面

访问 `/latex-test` 页面可以测试 LaTeX 渲染功能：
- 实时预览 LaTeX 公式渲染效果
- 测试各种数学公式语法
- 验证错误处理机制

## 性能考虑

- KaTeX 比 MathJax 更轻量，渲染速度更快
- 使用 `throwOnError: false` 确保错误不会中断渲染
- CSS 样式优化，减少重绘和重排

## 兼容性

- ✅ 支持所有现代浏览器
- ✅ 响应式设计，适配移动设备
- ✅ 暗色/亮色主题自动适配
- ✅ 与现有 Markdown 功能完全兼容

## 常见问题

### Q: 如何输入特殊字符？
A: 使用反斜杠转义，例如 `\{` `\}` `\$` 等。

### Q: 公式不显示怎么办？
A: 检查语法是否正确，查看浏览器控制台是否有错误信息。

### Q: 如何调整公式大小？
A: 块级公式会自动适配容器宽度，行内公式继承文本字体大小。

## 更新日志

- **v1.0.0** (2025-01-08): 初始版本，支持基本的 LaTeX 数学公式渲染
